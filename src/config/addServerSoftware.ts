import type { FactSheetData, ServerSoftware, WizardFormModel, WizardStep } from '../model'
import { SUB_APPLICATION_OWNER_TECHNICAL } from '../constants'
import { useLeaniX } from '../hooks/leanix'
import { lifecycleOrderControl } from './helpers'
import { getFieldByName } from '../helpers'

const { createFactSheet, updateServerSoftwareFactsheet, addSubscription, createAppRelation, getFactSheetByNameAndType, removeFactSheet } = useLeaniX()

export const addServerSoftware: WizardFormModel = {

  id: 'addServerSoftware',
  name: 'Add Server Software',
  originFactSheet: 'ITComponent',
  addWithTemporaryMode: false,
  onlyAdmin: false,
  saveErrorMessage: 'Error while saving the server software. Please try again',
  saveSuccessMessage: 'Server software successfully created',
  steps: [
    {
      name: 'Information',
      rules: [
        async (form, steps, _currentStep, _currentUser, update, deeplinkArgs) => {
          const newSteps: WizardStep[] = [...steps]

          // Handle Italy Component switch
          const italyComponent: boolean | undefined = form.getFieldValue('italyComponent')

          if (italyComponent !== undefined) {
            if (italyComponent) {
              form.setFieldValue('suffix', '(ITA)')

              // Set Italy as default business area owner when Italy Component is enabled
              const existing = await getFactSheetByNameAndType('ITA', 'UserGroup')
              if (existing.length > 0) {
                const current = form.getFieldValue('businessAreas')

                // Only set if no business areas are defined or only suggestions exist
                if (current === undefined || (current && current.length === 1 && current[0].suggestion && current[0].usageType === 'owner')) {
                  form.setFieldValue('businessAreas', [{
                    businessArea: existing[0].node.id,
                    usageType: 'owner',
                    suggestion: true
                  }])
                }
              }
            } else {
              form.setFieldValue('suffix', '')

              // Clear business areas if they were set as suggestion when disabling Italy Component
              const current = form.getFieldValue('businessAreas')
              if (current && current.length === 1 && current[0].suggestion && current[0].usageType === 'owner') {
                form.setFieldValue('businessAreas', [])
              }
            }
          }

          const suffix = form.getFieldValue('suffix')

          if (suffix) {
            const existing = await getFactSheetByNameAndType(suffix.replace('(', '').replace(')', ''), 'UserGroup')
            if (existing.length > 0) {
              const current = form.getFieldValue('businessAreas')

              if (current === undefined || (current && current.length === 1 && current[0].suggestion && current[0].usageType === 'owner')) {
                form.setFieldValue('businessAreas', [{
                  businessArea: existing[0].node.id,
                  usageType: 'owner',
                  suggestion: true
                }])
              }
            }
          }

          update(newSteps)
        },
        // Disable Tech Category when Italy Component is enabled
        async (form, steps, currentStep, _currentUser, update, deeplinkArgs) => {
          const italyComponent: boolean | undefined = form.getFieldValue('italyComponent')

          if (italyComponent !== undefined) {
            const newSteps: WizardStep[] = [...steps]
            const techCategoryField = getFieldByName(newSteps, currentStep, 'techCategory')

            // Disable techCategory when Italy Component is enabled
            techCategoryField.disabled = italyComponent

            newSteps[currentStep].fields = newSteps[currentStep].fields.map(f =>
              f.name === 'techCategory' ? techCategoryField : f
            )

            update(newSteps)
          }
        }
      ],
      fields: [
        {
          name: 'italyComponent',
          title: 'Italy Component',
          description: 'Enable this if this is an Italy-specific component',
          required: false,
          fieldType: 'Switch',
          visible: true,
          disabled: false
        },
        {
          title: 'Name',
          name: 'name',
          fieldType: 'ServerSoftwareName',
          description: 'The name is used to identify this Fact Sheet in the Inventory, Reporting and Search.',
          required: true,
          visible: true
        },
        {
          name: 'suffix',
          title: 'Suffix',
          fieldType: 'Hidden',
          description: 'Hidden field for suffix',
          required: false,
          visible: false
        },
        {
          name: 'appReference',
          title: 'Application Reference (if a suitable application exists)',
          description: 'Please provide a application reference. Based on this reference you will receive suggestions while entering the data.',
          required: false,
          fieldType: 'AppReferenceWithSuggestionSSW',
          addonBefore: undefined,
          visible: true,
          loadFactSheet: 'Application'
        },
        {
          name: 'description',
          title: 'Description',
          description: 'At least 1-2 sentences. What can be done with the software?',
          required: true,
          fieldType: 'TextArea',
          visible: true
        },
        {
          name: 'techCategory',
          title: 'Tech Category',
          description: 'Used to classify the IT Components and to show it in the Application Factsheet on the IT Components relation.',
          required: false,
          fieldType: 'SingleSelect',
          loadLeanIXOptions: true,
          visible: true
        },
        {
          name: 'provider',
          title: 'Provider',
          description: '',
          required: false,
          fieldType: 'SingleSelectProvider',
          loadFactSheet: 'ProviderId',
          visible: true
        },

      ],
      customChecks: async (form) => {
        let errorMessage = ''
        const inputName = form.getFieldValue('name')
        const suffix = form.getFieldValue('suffix')
        const italyComponent = form.getFieldValue('italyComponent')

        // Build the name based on whether Italy Component is enabled
        const name = italyComponent && suffix ? `${inputName.trim()} ${suffix}` : inputName.trim()

        const existing = await getFactSheetByNameAndType(name, 'ITComponent')

        if (existing.length > 0) {
          errorMessage = `An ITComponent with the name ${name} already exists.`
        }

        return errorMessage.length > 0 ? errorMessage : undefined
      }
    },
    {
      name: 'Business Area',
      rules: [],
      fields: [
        {
          name: 'businessAreas',
          title: 'Business Areas',
          description: 'Please choose whether this Business Area is owning or using this Application (only one owner allowed)',
          fieldType: 'BusinessAreaSelection',
          required: false,
          visible: true,
          loadFactSheet: 'UserGroup'
        }
      ],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Lifecycle',
      rules: [],
      fields: [
        {
          name: 'phaseIn',
          title: 'Phase in',
          description: 'Date since when the Application is in the phase of being built or acquired',
          fieldType: 'DatePicker',
          required: false,
          visible: true

        },
        {
          name: 'active',
          title: 'Go-Live',
          description: 'Date since when the Desktop Software is productive and in use',
          fieldType: 'DatePicker',
          required: false,
          visible: true

        },
        {
          name: 'phaseOut',
          title: 'Switch Off',
          description: 'Date since when the Application is in the phase of being retired',
          fieldType: 'DatePicker',
          required: false,
          visible: true
        }
      ],
      customChecks: async (form) => {
        const phaseIn = form.getFieldValue('phaseIn')
        const active = form.getFieldValue('active')
        const phaseOut = form.getFieldValue('phaseOut')

        return lifecycleOrderControl(phaseIn, active, phaseOut)
      }
    },
    {
      name: 'Subscriptions',
      rules: [],
      fields: [
        {
          name: 'applicationOwners',
          title: 'Application Owner (technical)',
          description: 'The Application Owner (technical) is responsible for maintenance, further development rsp. configuration as well as for the 2nd level support of the relevant application. He ensures that '
            + 'the business and technical requirements are implemented and that a reliable and costefficient '
            + 'operation is ensured in accordance with the business requirements and agreements.',
          fieldType: 'UserSelectMultiple',
          required: true,
          visible: true
        }
      ],
      customChecks: async () => {
        return undefined
      }
    }
  ],
  init: async (form, currentUser, transferData, factSheetId, validate, deepLinkArgs) => {
    // Initialize Italy Component switch based on deeplink parameter
    if (deepLinkArgs?.italy !== undefined) {
      form.setFieldValue('italyComponent', deepLinkArgs.italy)
      if (deepLinkArgs.italy) {
        form.setFieldValue('suffix', '(ITA)')

        // Set Italy as default business area owner
        const existing = await getFactSheetByNameAndType('ITA', 'UserGroup')
        if (existing.length > 0) {
          form.setFieldValue('businessAreas', [{
            businessArea: existing[0].node.id,
            usageType: 'owner',
            suggestion: true
          }])
        }
      } else {
        form.setFieldValue('suffix', '')
      }
    } else {
      // Default to false if no deeplink parameter
      form.setFieldValue('italyComponent', false)
      form.setFieldValue('suffix', '')
    }
  },
  save: async (data: FactSheetData) => {
    console.log('DATA SAVE', data)
    console.log('Italy Component:', data.italyComponent)
    console.log('Suffix:', data.suffix)
    let compId = ''

    try {
      // Build the name based on whether Italy Component is enabled
      const name = data.italyComponent && data.suffix ? `${data.name.trim()} ${data.suffix}` : data.name.trim()
      console.log('Final name:', name)

      await createFactSheet(name, 'ITComponent', false).then(async (factSheet) => {
        if (factSheet?.createFactSheet?.factSheet?.id) {
          compId = factSheet.createFactSheet.factSheet.id

          const completeData: ServerSoftware = {
            id: factSheet.createFactSheet.factSheet.id,
            name,
            description: data.description,
            appReference: data.appReference,
            provider: data.provider,
            techCategory: data.techCategory,
            businessAreas: data.businessAreas,
            phaseIn: data.phaseIn,
            active: data.active,
            phaseOut: data.phaseOut,
            applicationOwners: data.applicationOwners
          }

          await updateServerSoftwareFactsheet(completeData.id, completeData).then(async (result) => {
            console.log('UPDATED', result)

            // RELATION APP
            if (completeData.appReference && completeData.appReference.length > 0) {
              await createAppRelation(completeData.id, completeData.appReference).catch((error) => {
                console.error('error', error)
                throw new Error('Error while creating subscriptions. Please try it again.')
              })
            }

            // ADD RELATIONS
            for (const appOwner of completeData.applicationOwners) {
              console.log(`NEW SUB FOR ${appOwner} for application ${completeData.id}`)
              await addSubscription(completeData.id, appOwner, [SUB_APPLICATION_OWNER_TECHNICAL]).catch((error) => {
                console.error('error', error)
                throw new Error('Error while creating subscription. Please try it again.')
              })
            }
          }).catch((error) => {
            console.error('error', error)
            throw new Error('Error while create Server Software. Please try it again.')
          })
        }
      }).catch((error) => {
        console.error('ERROR', error)
        throw new Error('Error while creating Server Software. Please try it again.')
      })
    }
    catch (err) {
      console.error('ERROR', err)
      console.log(compId)
      if (compId) {
        await removeFactSheet(compId)
      }
      return undefined
    }
    return compId
  }
}
