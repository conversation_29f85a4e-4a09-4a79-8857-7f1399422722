import type { FormInstance } from 'antd'
import type { Value } from '../../model'
import { ExclamationCircleOutlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Form, Radio, Select, Space, Tooltip } from 'antd'
import React, { useCallback, useEffect, useState } from 'react'

interface Props {
  businessAreas: Value[]
  form: FormInstance
  required?: boolean
}

interface BusinessAreaField {
  businessArea?: string
  usageType?: 'user' | 'owner'
  existing?: boolean
}

const FIELD_NAME = 'businessAreas'

const BusinessAreaSelectionInput = ({ businessAreas, form, required = true }: Props) => {
  const [duplicateSelections, setDuplicateSelections] = useState<number[]>([])
  const [ownerIndices, setOwnerIndices] = useState<number[]>([])
  const [emptyFields, setEmptyFields] = useState<{ [key: string]: boolean }>({})
  const [hasOwner, setHasOwner] = useState(false)

  const checkForDuplicatesAndEmpty = useCallback((businessAreas: BusinessAreaField[]) => {
    const selectedValues = new Set()
    const selectionDuplicates: number[] = []
    const ownerIndexes: number[] = []
    const newEmptyFields: { [key: string]: boolean } = {}
    let ownerFound = false

    businessAreas.forEach((businessArea, index) => {
      const selection = businessArea?.businessArea
      const usageType = businessArea?.usageType
      const isExisting = businessArea?.existing

      if (selection) {
        if (selectedValues.has(selection)) {
          selectionDuplicates.push(index)
        }
        else {
          selectedValues.add(selection)
        }
      }
      else {
        newEmptyFields[`${FIELD_NAME}.${index}.businessArea`] = true
      }

      if (usageType === 'owner') {
        ownerIndexes.push(index)
        ownerFound = true
      }
      else if (!usageType && !isExisting) {
        newEmptyFields[`${FIELD_NAME}.${index}.usageType`] = true
      }
    })

    setDuplicateSelections(selectionDuplicates)
    setOwnerIndices(ownerIndexes)
    setEmptyFields(newEmptyFields)
    setHasOwner(ownerFound)

    return { selectionDuplicates, ownerIndexes, newEmptyFields }
  }, [])

  const updateErrorState = useCallback(() => {
    const currentBusinessAreas = form.getFieldValue(FIELD_NAME) || []
    checkForDuplicatesAndEmpty(currentBusinessAreas)
  }, [form, checkForDuplicatesAndEmpty])

  useEffect(() => {
    updateErrorState()
  }, [updateErrorState])

  const handleFieldChange = useCallback(() => {
    setTimeout(updateErrorState, 0)
  }, [updateErrorState])

  return (
    <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
      <Form.List
        name={FIELD_NAME}
        rules={[
          {
            validator: async (_, businessAreas: BusinessAreaField[]) => {
              const { selectionDuplicates, newEmptyFields, ownerIndexes } = checkForDuplicatesAndEmpty(businessAreas || [])

              if (selectionDuplicates.length > 0) {
                return Promise.reject(new Error('Duplicate business area selections are not allowed'))
              }

              if (Object.keys(newEmptyFields).length > 0) {
                return Promise.reject(new Error('Please fill in all required fields'))
              }

              if (required && ownerIndexes.length === 0) {
                return Promise.reject(new Error('At least one Owner must be selected'))
              }

              return Promise.resolve()
            }
          }
        ]}
      >
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }, index) => {
              const isSelectionDuplicate = duplicateSelections.includes(index)
              const isOwnerDuplicate = ownerIndices.length > 1 && ownerIndices.includes(index)
              const isSelectionEmpty = emptyFields[`${FIELD_NAME}.${index}.businessArea`]
              const isUsageTypeEmpty = emptyFields[`${FIELD_NAME}.${index}.usageType`]
              const isExisting = form.getFieldValue([FIELD_NAME, name, 'existing'])
              const disableOwner = hasOwner && !isExisting

              return (
                <Space key={key} style={{ display: 'flex' }} align="baseline">
                  <Form.Item
                    {...restField}
                    name={[name, 'businessArea']}
                    help={false}
                    rules={[
                      { required: true, message: 'Please select an business area' },
                      {
                        validator: (_, value) => {
                          if (isSelectionDuplicate) {
                            return Promise.reject(new Error('Duplicate business area selection'))
                          }
                          return Promise.resolve()
                        }
                      }
                    ]}
                    validateStatus={isSelectionDuplicate || isSelectionEmpty ? 'error' : ''}
                    hasFeedback={isSelectionDuplicate || isSelectionEmpty}
                  >
                    <Select
                      placeholder="Select option"
                      style={{ minWidth: '200px' }}
                      options={businessAreas}
                      onChange={handleFieldChange}
                      filterOption={(input, option) => (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())}
                      filterSort={(optionA, optionB) =>
                        (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
                      showSearch
                    />
                  </Form.Item>

                  <Form.Item
                    {...restField}
                    name={[name, 'usageType']}
                    help={false}
                    rules={[
                      {
                        required: !isExisting,
                        message: 'Please select a usage type'
                      }
                    ]}
                    validateStatus={isOwnerDuplicate ? 'warning' : (isUsageTypeEmpty ? 'error' : '')}
                    hasFeedback={isOwnerDuplicate || isUsageTypeEmpty}
                  >
                    <Radio.Group onChange={handleFieldChange}>
                      <Radio value="owner" disabled={disableOwner}>Owner</Radio>
                      <Radio value="user">User</Radio>
                    </Radio.Group>
                  </Form.Item>

                  {(isSelectionDuplicate || isSelectionEmpty) && (
                    <Tooltip title={isSelectionDuplicate ? 'Duplicate business area found!' : 'Please select a business area'}>
                      <ExclamationCircleOutlined style={{ color: 'red' }} />
                    </Tooltip>
                  )}

                  {(isOwnerDuplicate || (isUsageTypeEmpty && !isExisting)) && (
                    <Tooltip title={isOwnerDuplicate ? 'It is recommended to select only 1 Owner' : 'Please select a usage type'}>
                      <ExclamationCircleOutlined style={{ color: isOwnerDuplicate ? 'orange' : 'red' }} />
                    </Tooltip>
                  )}

                  <MinusCircleOutlined onClick={() => {
                    remove(name)
                    setTimeout(updateErrorState, 0)
                  }}
                  />
                </Space>
              )
            })}
            <Form.Item>
              <Space align="baseline">
                <Button
                  type="dashed"
                  onClick={() => {
                    add()
                    setTimeout(updateErrorState, 0)
                  }}
                  icon={<PlusOutlined />}
                >
                  Add Business Area
                </Button>
                {required && !hasOwner && (
                  <Tooltip title="At least one Owner must be selected">
                    <ExclamationCircleOutlined style={{ color: 'red' }} />
                  </Tooltip>
                )}
              </Space>
            </Form.Item>
          </>
        )}
      </Form.List>
    </div>
  )
}

export default BusinessAreaSelectionInput
