import type { FormInstance } from 'antd'
import type { Value, WizardField } from '../../model'
import { AutoComplete, DatePicker, Form, Input, Select, Skeleton, Switch, Typography } from 'antd'
import TextArea from 'antd/es/input/TextArea'
import React, { useEffect, useState } from 'react'
import { API_MIDDLEWARE } from '../../constants'
import { useLeaniX } from '../../hooks/leanix'
import DesktopSoftwareSuggestionField from '../suggestions/desktopSoftware/DesktopSoftwareSuggestionField'
import ServerSoftwareSuggestionField from '../suggestions/serverSoftware/ServerSoftwareSuggestionField'
import BusinessAreaSelectionInput from './BusinessAreaSelectionInput'
import ExpectedApplicationSteps from './ExpectedApplicationSteps'
import Form<PERSON>abel from './FormLabel'
import NameField from './NameField'
import ProviderSelect from './ProviderSelect'
import SelectInterfaceTable from './SelectInterfaceTable'
import ServerSoftwareName from './ServerSoftwareName'
import UserSelectLeanIX from './UserSelectLeanIX'
import UserSelectLeanIXMultiple from './UserSelectLeanIXMultiple'
import MultipleSelectFieldDefinition = lxr.MultipleSelectFieldDefinition
import ApplicationSelect from "./ApplicationSelect";

interface Props {
  field: WizardField
  factSheetType: string
  form: FormInstance
  newCustomProviders: Value[]
  setNewCustomProviders: React.Dispatch<React.SetStateAction<Value[]>>
}

export default function Field({ field, factSheetType, form, newCustomProviders, setNewCustomProviders }: Props) {
  const { getSelectData, getProviders, getApplications, getDesktopSoftware, getTags, getFactSheetsByIds } = useLeaniX()
  const [options, setOptions] = useState<Value[] | undefined>(undefined)
  const [loading, setLoading] = useState(true)

  const { Text } = Typography

  const fullWidthInput: boolean = field.fieldType === 'SelectFactsheetTable'

  const getFieldValues = (fieldName: string) => {
    const fieldValues = (lx.currentSetup.settings.dataModel.factSheets[factSheetType].fields[fieldName] as MultipleSelectFieldDefinition).values
    const translatedValues: Value[] = []

    fieldValues.map((fieldValue) => {
      translatedValues.push({
        label: lx.translateFieldValue(factSheetType, fieldName, fieldValue),
        value: fieldValue
      })
    })

    return {
      label: lx.translateField(factSheetType, fieldName),
      values: [...translatedValues]
    }
  }

  useEffect(() => {
    if (field.loadFactSheet === 'Application') {
      getApplications().then(data => setOptions(data)).then(() => setLoading(false))
    }
    else if (field.loadFactSheet === 'Provider' || field.loadFactSheet === 'ProviderId') {
      getProviders(field.loadFactSheet === 'Provider').then(data => setOptions(data)).then(() => setLoading(false))
    }
    else if (field.loadFactSheet === 'DesktopSoftware') {
      getDesktopSoftware().then(data => setOptions(data)).then(() => setLoading(false))
    }
    else if (field.loadFactSheet === 'Middleware') {
      getFactSheetsByIds(API_MIDDLEWARE).then((data) => {
        console.log(data)
        const parsed: Value[] = data?.allFactSheets.edges.map((factSheet: any) => ({
          label: factSheet.node.displayName,
          value: factSheet.node.id
        }))
        return parsed
      }).then(result => setOptions(result)).then(() => setLoading(false))
    }
    else if (field.loadFactSheet) {
      getSelectData(field.loadFactSheet).then(data => setOptions(data)).then(() => setLoading(false))
    }
    else if (field && field.loadLeanIXOptions) {
      const data = getFieldValues(field.name)
      setOptions(data.values)
      setLoading(false)
    }
    else if (field && field.loadTag && field.loadTag.length > 0) {
      getTags(field.loadTag).then(data => setOptions(data)).then(() => setLoading(false))
    }
    else {
      setLoading(false)
    }
  }, [])

  if (field.visible || field.visible == undefined) {
    return (
      <Form.Item
        labelCol={fullWidthInput ? { span: 0 } : undefined}
        wrapperCol={fullWidthInput ? { span: 24 } : undefined}
        label={<FormLabel title={field.title} text={field.description} />}
        name={field.name}
        valuePropName={field.fieldType == 'Switch' ? 'checked' : undefined}
        rules={[{ required: field.required, message: `Please provide a ${field.title}!` }]}
      >

        {loading
          ? <Skeleton.Input block active />
          : (
              <>
                {(field.fieldType === 'SingleSelect' || field.fieldType === 'MultiSelect') && (
                  <Select
                    disabled={field.disabled}
                    mode={field.fieldType === 'MultiSelect' ? 'multiple' : undefined}
                    showSearch
                    placeholder="Search to Select"
                    optionFilterProp="children"
                    filterOption={(input, option) => (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
                    options={(field.loadLeanIXOptions || field.loadFactSheet || field.loadTag) ? options : field.options}
                  />
                )}

                {field.fieldType === 'ApplicationSelect' && (
                    <ApplicationSelect
                        field={field}
                        options={options}

                    />
                )}

                {field.fieldType === 'SelectFactsheetTable' && (
                  <SelectInterfaceTable fieldName={field.name} form={form} />
                )}

                {field.fieldType === 'MultiSelectProvider' && (
                  <ProviderSelect
                    mode="multiple"
                    providers={options}
                    options={(field.loadLeanIXOptions || field.loadFactSheet) ? options : field.options}
                    newCustomProviders={newCustomProviders}
                    setNewCustomProviders={setNewCustomProviders}
                  />
                )}
                {field.fieldType === 'SingleSelectProvider' && (
                  <ProviderSelect
                    mode={undefined}
                    providers={options}
                    options={(field.loadLeanIXOptions || field.loadFactSheet) ? options : field.options}
                    newCustomProviders={newCustomProviders}
                    setNewCustomProviders={setNewCustomProviders}
                  />
                )}

                {field.fieldType === 'BusinessAreaSelection' && (
                  <BusinessAreaSelectionInput businessAreas={options!} form={form} required={field.required} />
                )}

                {field.fieldType === 'Text' && (<Input addonBefore={field.addonBefore} placeholder={field.title} />)}
                {field.fieldType === 'NameField' && <NameField required={field.required} disabled={field.disabled} form={form} isExternal={form.getFieldValue('external')} />}
                {field.fieldType === 'ServerSoftwareName'
                && <ServerSoftwareName required={field.required} disabled={field.disabled} form={form} />}
                {field.fieldType === 'UserSelect' && <UserSelectLeanIX />}
                {field.fieldType === 'UserSelectMultiple' && <UserSelectLeanIXMultiple data={undefined} />}
                {field.fieldType === 'Switch'
                && <Switch disabled={field.disabled} style={{ display: 'block', marginLeft: '16px' }} />}
                {field.fieldType === 'TextArea' && <TextArea placeholder={field.title} rows={3} />}
                {field.fieldType === 'ReadOnlyComment' && <Text>{form.getFieldValue(field.name)}</Text>}
                {field.fieldType === 'DatePicker' && <DatePicker />}
                {field.fieldType === 'AppReferenceWithSuggestionDSW' && (<DesktopSoftwareSuggestionField form={form} />)}
                {field.fieldType === 'AppReferenceWithSuggestionSSW' && (<ServerSoftwareSuggestionField form={form} />)}
                {field.fieldType === 'ExpectedSteps' && (<ExpectedApplicationSteps form={form} />)}
                {field.fieldType === 'AutoCompleteField' && (
                  <AutoComplete
                    options={(field.loadLeanIXOptions || field.loadFactSheet) ? options : field.options}
                    filterOption={(input, option) => (option?.label.toLowerCase() ?? '').includes(input.toLowerCase())}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())}
                    placeholder="Search to select"
                  />
                )}
              </>
            )}
      </Form.Item>
    )
  }
  return undefined
}
